<?php $__env->startSection('title', 'Quản lý dịch vụ khách hàng'); ?>
<?php $__env->startSection('page-title', 'Quản lý dịch vụ khách hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-link me-2"></i>
            Danh sách dịch vụ khách hàng
        </h5>
        <a href="<?php echo e(route('admin.customer-services.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Gán dịch vụ mới
        </a>
    </div>
    
    <div class="card-body">
        <!-- Filters -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Tìm kiếm khách hàng hoặc dịch vụ..."
                               value="<?php echo e(request('search')); ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="filter" class="form-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active" <?php echo e(request('filter') === 'active' ? 'selected' : ''); ?>>
                            Đang hoạt động
                        </option>
                        <option value="expiring" <?php echo e(request('filter') === 'expiring' ? 'selected' : ''); ?>>
                            Sắp hết hạn
                        </option>
                        <option value="expired" <?php echo e(request('filter') === 'expired' ? 'selected' : ''); ?>>
                            Đã hết hạn
                        </option>
                    </select>
                </div>
                <div class="col-md-5">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Lọc
                    </button>
                    <?php if(request()->hasAny(['search', 'filter'])): ?>
                        <a href="<?php echo e(route('admin.customer-services.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </form>
        
        <!-- Quick Filter Buttons -->
        <div class="mb-3">
            <div class="btn-group" role="group">
                <a href="<?php echo e(route('admin.customer-services.index')); ?>" 
                   class="btn btn-sm <?php echo e(!request('filter') ? 'btn-primary' : 'btn-outline-primary'); ?>">
                    Tất cả
                </a>
                <a href="<?php echo e(route('admin.customer-services.index', ['filter' => 'active'])); ?>" 
                   class="btn btn-sm <?php echo e(request('filter') === 'active' ? 'btn-success' : 'btn-outline-success'); ?>">
                    Hoạt động
                </a>
                <a href="<?php echo e(route('admin.customer-services.index', ['filter' => 'expiring'])); ?>" 
                   class="btn btn-sm <?php echo e(request('filter') === 'expiring' ? 'btn-warning' : 'btn-outline-warning'); ?>">
                    Sắp hết hạn
                </a>
                <a href="<?php echo e(route('admin.customer-services.index', ['filter' => 'expired'])); ?>" 
                   class="btn btn-sm <?php echo e(request('filter') === 'expired' ? 'btn-danger' : 'btn-outline-danger'); ?>">
                    Đã hết hạn
                </a>
            </div>
        </div>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">
                Hiển thị <?php echo e($customerServices->firstItem() ?? 0); ?> - <?php echo e($customerServices->lastItem() ?? 0); ?> 
                trong tổng số <?php echo e($customerServices->total()); ?> dịch vụ
            </div>
        </div>
        
        <!-- Customer Services Table -->
        <?php if($customerServices->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Khách hàng</th>
                            <th>Dịch vụ</th>
                            <th>Email đăng nhập</th>
                            <th>Kích hoạt</th>
                            <th>Hết hạn</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $customerServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo e($service->customer->name); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo e($service->customer->customer_code); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e($service->servicePackage->name); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo e($service->servicePackage->category->name); ?> • 
                                            <?php echo e($service->servicePackage->account_type); ?>

                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo e($service->login_email); ?></code>
                                </td>
                                <td>
                                    <small><?php echo e($service->activated_at->format('d/m/Y')); ?></small>
                                </td>
                                <td>
                                    <div>
                                        <?php echo e($service->expires_at->format('d/m/Y')); ?>

                                        <br>
                                        <small class="text-muted">
                                            <?php if($service->isExpired()): ?>
                                                <span class="text-danger">Đã hết hạn</span>
                                            <?php elseif($service->isExpiringSoon()): ?>
                                                <span class="text-warning">Còn <?php echo e($service->getDaysRemaining()); ?> ngày</span>
                                            <?php else: ?>
                                                <span class="text-success">Còn <?php echo e($service->getDaysRemaining()); ?> ngày</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <?php if($service->status === 'active'): ?>
                                        <?php if($service->isExpired()): ?>
                                            <span class="badge bg-danger">Hết hạn</span>
                                        <?php elseif($service->isExpiringSoon()): ?>
                                            <span class="badge bg-warning">Sắp hết hạn</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">Hoạt động</span>
                                        <?php endif; ?>
                                    <?php elseif($service->status === 'expired'): ?>
                                        <span class="badge bg-danger">Hết hạn</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Đã hủy</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.customers.show', $service->customer)); ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem khách hàng">
                                            <i class="fas fa-user"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.customer-services.edit', $service)); ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" 
                                              action="<?php echo e(route('admin.customer-services.destroy', $service)); ?>" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                <?php echo e($customerServices->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-link fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy dịch vụ nào</h5>
                <?php if(request()->hasAny(['search', 'filter'])): ?>
                    <p class="text-muted">Thử thay đổi bộ lọc hoặc <a href="<?php echo e(route('admin.customer-services.index')); ?>">xóa bộ lọc</a></p>
                <?php else: ?>
                    <p class="text-muted">Hãy <a href="<?php echo e(route('admin.customer-services.create')); ?>">gán dịch vụ đầu tiên</a></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/customer-services/index.blade.php ENDPATH**/ ?>
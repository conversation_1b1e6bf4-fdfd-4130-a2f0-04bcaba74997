<?php $__env->startSection('title', 'Quản lý khách hàng'); ?>
<?php $__env->startSection('page-title', 'Quản lý khách hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>
            Danh sách khách hàng
        </h5>
        <a href="<?php echo e(route('admin.customers.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Thêm khách hàng
        </a>
    </div>
    
    <div class="card-body">
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Tìm kiếm theo tên, email, SĐT hoặc mã khách hàng..."
                               value="<?php echo e(request('search')); ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Tìm kiếm
                    </button>
                    <?php if(request('search')): ?>
                        <a href="<?php echo e(route('admin.customers.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </form>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">
                Hiển thị <?php echo e($customers->firstItem() ?? 0); ?> - <?php echo e($customers->lastItem() ?? 0); ?> 
                trong tổng số <?php echo e($customers->total()); ?> khách hàng
            </div>
        </div>
        
        <!-- Customers Table -->
        <?php if($customers->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mã KH</th>
                            <th>Tên khách hàng</th>
                            <th>Email</th>
                            <th>Số điện thoại</th>
                            <th>Số dịch vụ</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($customer->customer_code); ?></span>
                                </td>
                                <td>
                                    <strong><?php echo e($customer->name); ?></strong>
                                </td>
                                <td>
                                    <?php if($customer->email): ?>
                                        <a href="mailto:<?php echo e($customer->email); ?>" class="text-decoration-none">
                                            <?php echo e($customer->email); ?>

                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa có</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($customer->phone): ?>
                                        <a href="tel:<?php echo e($customer->phone); ?>" class="text-decoration-none">
                                            <?php echo e($customer->phone); ?>

                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa có</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        <?php echo e($customer->customerServices->count()); ?> dịch vụ
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo e($customer->created_at->format('d/m/Y H:i')); ?>

                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.customers.show', $customer)); ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.customers.edit', $customer)); ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.customers.assign-service', $customer)); ?>" 
                                           class="btn btn-sm btn-outline-success" 
                                           title="Gán dịch vụ">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <form method="POST" 
                                              action="<?php echo e(route('admin.customers.destroy', $customer)); ?>" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa khách hàng này?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                <?php echo e($customers->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy khách hàng nào</h5>
                <?php if(request('search')): ?>
                    <p class="text-muted">Thử thay đổi từ khóa tìm kiếm hoặc <a href="<?php echo e(route('admin.customers.index')); ?>">xóa bộ lọc</a></p>
                <?php else: ?>
                    <p class="text-muted">Hãy <a href="<?php echo e(route('admin.customers.create')); ?>">thêm khách hàng đầu tiên</a></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/customers/index.blade.php ENDPATH**/ ?>
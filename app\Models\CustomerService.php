<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerService extends Model
{
    protected $fillable = [
        'customer_id',
        'service_package_id',
        'login_email',
        'login_password',
        'activated_at',
        'expires_at',
        'status',
        'internal_notes',
    ];

    protected $casts = [
        'activated_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function servicePackage(): BelongsTo
    {
        return $this->belongsTo(ServicePackage::class);
    }

    // Kiểm tra xem dịch vụ có sắp hết hạn không (trong vòng 7 ngày)
    public function isExpiringSoon(): bool
    {
        return $this->expires_at && $this->expires_at->diffInDays(now()) <= 7 && $this->expires_at->isFuture();
    }

    // Kiểm tra xem dịch vụ đã hết hạn chưa
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    // Scope để lấy các dịch vụ đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Scope để lấy các dịch vụ sắp hết hạn
    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('status', 'active')
            ->where('expires_at', '>', now())
            ->where('expires_at', '<=', now()->addDays($days));
    }

    // Scope để lấy các dịch vụ đã hết hạn
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }
}

@extends('layouts.admin')

@section('title', 'Quản lý dịch vụ khách hàng')
@section('page-title', 'Quản lý dịch vụ khách hàng')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-link me-2"></i>
            Danh sách dịch vụ khách hàng
        </h5>
        <a href="{{ route('admin.customer-services.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Gán dịch vụ mới
        </a>
    </div>
    
    <div class="card-body">
        <!-- Filters -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Tìm kiếm khách hàng hoặc dịch vụ..."
                               value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="filter" class="form-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active" {{ request('filter') === 'active' ? 'selected' : '' }}>
                            Đang hoạt động
                        </option>
                        <option value="expiring" {{ request('filter') === 'expiring' ? 'selected' : '' }}>
                            Sắp hết hạn
                        </option>
                        <option value="expired" {{ request('filter') === 'expired' ? 'selected' : '' }}>
                            Đã hết hạn
                        </option>
                    </select>
                </div>
                <div class="col-md-5">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Lọc
                    </button>
                    @if(request()->hasAny(['search', 'filter']))
                        <a href="{{ route('admin.customer-services.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </a>
                    @endif
                </div>
            </div>
        </form>
        
        <!-- Quick Filter Buttons -->
        <div class="mb-3">
            <div class="btn-group" role="group">
                <a href="{{ route('admin.customer-services.index') }}" 
                   class="btn btn-sm {{ !request('filter') ? 'btn-primary' : 'btn-outline-primary' }}">
                    Tất cả
                </a>
                <a href="{{ route('admin.customer-services.index', ['filter' => 'active']) }}" 
                   class="btn btn-sm {{ request('filter') === 'active' ? 'btn-success' : 'btn-outline-success' }}">
                    Hoạt động
                </a>
                <a href="{{ route('admin.customer-services.index', ['filter' => 'expiring']) }}" 
                   class="btn btn-sm {{ request('filter') === 'expiring' ? 'btn-warning' : 'btn-outline-warning' }}">
                    Sắp hết hạn
                </a>
                <a href="{{ route('admin.customer-services.index', ['filter' => 'expired']) }}" 
                   class="btn btn-sm {{ request('filter') === 'expired' ? 'btn-danger' : 'btn-outline-danger' }}">
                    Đã hết hạn
                </a>
            </div>
        </div>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">
                Hiển thị {{ $customerServices->firstItem() ?? 0 }} - {{ $customerServices->lastItem() ?? 0 }} 
                trong tổng số {{ $customerServices->total() }} dịch vụ
            </div>
        </div>
        
        <!-- Customer Services Table -->
        @if($customerServices->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Khách hàng</th>
                            <th>Dịch vụ</th>
                            <th>Email đăng nhập</th>
                            <th>Kích hoạt</th>
                            <th>Hết hạn</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customerServices as $service)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $service->customer->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $service->customer->customer_code }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $service->servicePackage->name }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            {{ $service->servicePackage->category->name }} • 
                                            {{ $service->servicePackage->account_type }}
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <code>{{ $service->login_email }}</code>
                                </td>
                                <td>
                                    <small>{{ $service->activated_at->format('d/m/Y') }}</small>
                                </td>
                                <td>
                                    <div>
                                        {{ $service->expires_at->format('d/m/Y') }}
                                        <br>
                                        <small class="text-muted">
                                            @if($service->isExpired())
                                                <span class="text-danger">Đã hết hạn</span>
                                            @elseif($service->isExpiringSoon())
                                                <span class="text-warning">Còn {{ $service->getDaysRemaining() }} ngày</span>
                                            @else
                                                <span class="text-success">Còn {{ $service->getDaysRemaining() }} ngày</span>
                                            @endif
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    @if($service->status === 'active')
                                        @if($service->isExpired())
                                            <span class="badge bg-danger">Hết hạn</span>
                                        @elseif($service->isExpiringSoon())
                                            <span class="badge bg-warning">Sắp hết hạn</span>
                                        @else
                                            <span class="badge bg-success">Hoạt động</span>
                                        @endif
                                    @elseif($service->status === 'expired')
                                        <span class="badge bg-danger">Hết hạn</span>
                                    @else
                                        <span class="badge bg-secondary">Đã hủy</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.customers.show', $service->customer) }}" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem khách hàng">
                                            <i class="fas fa-user"></i>
                                        </a>
                                        <a href="{{ route('admin.customer-services.edit', $service) }}" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" 
                                              action="{{ route('admin.customer-services.destroy', $service) }}" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $customerServices->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-link fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy dịch vụ nào</h5>
                @if(request()->hasAny(['search', 'filter']))
                    <p class="text-muted">Thử thay đổi bộ lọc hoặc <a href="{{ route('admin.customer-services.index') }}">xóa bộ lọc</a></p>
                @else
                    <p class="text-muted">Hãy <a href="{{ route('admin.customer-services.create') }}">gán dịch vụ đầu tiên</a></p>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection

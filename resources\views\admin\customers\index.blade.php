@extends('layouts.admin')

@section('title', 'Quản lý khách hàng')
@section('page-title', 'Quản lý khách hàng')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>
            <PERSON>h sách khách hàng
        </h5>
        <a href="{{ route('admin.customers.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Thêm khách hàng
        </a>
    </div>
    
    <div class="card-body">
        <!-- Search Form -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Tìm kiếm theo tên, email, SĐT hoặc mã khách hàng..."
                               value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Tìm kiếm
                    </button>
                    @if(request('search'))
                        <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </a>
                    @endif
                </div>
            </div>
        </form>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">
                Hiển thị {{ $customers->firstItem() ?? 0 }} - {{ $customers->lastItem() ?? 0 }} 
                trong tổng số {{ $customers->total() }} khách hàng
            </div>
        </div>
        
        <!-- Customers Table -->
        @if($customers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mã KH</th>
                            <th>Tên khách hàng</th>
                            <th>Email</th>
                            <th>Số điện thoại</th>
                            <th>Số dịch vụ</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customers as $customer)
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ $customer->customer_code }}</span>
                                </td>
                                <td>
                                    <strong>{{ $customer->name }}</strong>
                                </td>
                                <td>
                                    @if($customer->email)
                                        <a href="mailto:{{ $customer->email }}" class="text-decoration-none">
                                            {{ $customer->email }}
                                        </a>
                                    @else
                                        <span class="text-muted">Chưa có</span>
                                    @endif
                                </td>
                                <td>
                                    @if($customer->phone)
                                        <a href="tel:{{ $customer->phone }}" class="text-decoration-none">
                                            {{ $customer->phone }}
                                        </a>
                                    @else
                                        <span class="text-muted">Chưa có</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ $customer->customerServices->count() }} dịch vụ
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $customer->created_at->format('d/m/Y H:i') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.customers.show', $customer) }}" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.customers.edit', $customer) }}" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('admin.customers.assign-service', $customer) }}" 
                                           class="btn btn-sm btn-outline-success" 
                                           title="Gán dịch vụ">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <form method="POST" 
                                              action="{{ route('admin.customers.destroy', $customer) }}" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa khách hàng này?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $customers->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy khách hàng nào</h5>
                @if(request('search'))
                    <p class="text-muted">Thử thay đổi từ khóa tìm kiếm hoặc <a href="{{ route('admin.customers.index') }}">xóa bộ lọc</a></p>
                @else
                    <p class="text-muted">Hãy <a href="{{ route('admin.customers.create') }}">thêm khách hàng đầu tiên</a></p>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection

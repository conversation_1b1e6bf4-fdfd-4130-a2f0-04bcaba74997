<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\ServicePackage;
use App\Models\CustomerService;
use Illuminate\Http\Request;

class CustomerServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CustomerService::with(['customer', 'servicePackage.category']);

        // Filter by status
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'expiring':
                    $query->expiringSoon();
                    break;
                case 'expired':
                    $query->expired();
                    break;
                case 'active':
                    $query->active();
                    break;
            }
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('customer', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('customer_code', 'like', "%{$search}%");
            })->orWhereHas('servicePackage', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        $customerServices = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.customer-services.index', compact('customerServices'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::orderBy('name')->get();
        $servicePackages = ServicePackage::with('category')->active()->orderBy('name')->get();

        return view('admin.customer-services.create', compact('customers', 'servicePackages'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'service_package_id' => 'required|exists:service_packages,id',
            'login_email' => 'required|email|max:255',
            'login_password' => 'nullable|string|max:255',
            'activated_at' => 'required|date',
            'expires_at' => 'required|date|after:activated_at',
            'status' => 'required|in:active,expired,cancelled',
            'internal_notes' => 'nullable|string',
        ]);

        CustomerService::create($request->all());

        return redirect()->route('admin.customer-services.index')
            ->with('success', 'Dịch vụ đã được gán thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(CustomerService $customerService)
    {
        $customerService->load(['customer', 'servicePackage.category']);

        return view('admin.customer-services.show', compact('customerService'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CustomerService $customerService)
    {
        $customers = Customer::orderBy('name')->get();
        $servicePackages = ServicePackage::with('category')->active()->orderBy('name')->get();

        return view('admin.customer-services.edit', compact('customerService', 'customers', 'servicePackages'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CustomerService $customerService)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'service_package_id' => 'required|exists:service_packages,id',
            'login_email' => 'required|email|max:255',
            'login_password' => 'nullable|string|max:255',
            'activated_at' => 'required|date',
            'expires_at' => 'required|date|after:activated_at',
            'status' => 'required|in:active,expired,cancelled',
            'internal_notes' => 'nullable|string',
        ]);

        $customerService->update($request->all());

        return redirect()->route('admin.customer-services.index')
            ->with('success', 'Thông tin dịch vụ đã được cập nhật!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CustomerService $customerService)
    {
        $customerService->delete();

        return redirect()->route('admin.customer-services.index')
            ->with('success', 'Dịch vụ đã được xóa!');
    }

    /**
     * Show form to assign service to customer
     */
    public function assignForm(Customer $customer)
    {
        $servicePackages = ServicePackage::with('category')->active()->orderBy('name')->get();

        return view('admin.customer-services.assign', compact('customer', 'servicePackages'));
    }

    /**
     * Assign service to customer
     */
    public function assignService(Request $request, Customer $customer)
    {
        $request->validate([
            'service_package_id' => 'required|exists:service_packages,id',
            'login_email' => 'required|email|max:255',
            'login_password' => 'nullable|string|max:255',
            'activated_at' => 'required|date',
            'expires_at' => 'required|date|after:activated_at',
            'internal_notes' => 'nullable|string',
        ]);

        CustomerService::create([
            'customer_id' => $customer->id,
            'service_package_id' => $request->service_package_id,
            'login_email' => $request->login_email,
            'login_password' => $request->login_password,
            'activated_at' => $request->activated_at,
            'expires_at' => $request->expires_at,
            'status' => 'active',
            'internal_notes' => $request->internal_notes,
        ]);

        return redirect()->route('admin.customers.show', $customer)
            ->with('success', 'Dịch vụ đã được gán cho khách hàng thành công!');
    }
}

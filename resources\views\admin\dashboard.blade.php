@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Thống kê tổng quan -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h3 class="mb-1">{{ number_format($totalCustomers) }}</h3>
                <p class="text-muted mb-0">Kh<PERSON>ch hàng</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-box fa-2x"></i>
                </div>
                <h3 class="mb-1">{{ number_format($totalServicePackages) }}</h3>
                <p class="text-muted mb-0">G<PERSON><PERSON> d<PERSON>ch vụ</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-1">{{ number_format($totalActiveServices) }}</h3>
                <p class="text-muted mb-0">Dịch vụ hoạt động</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
                <h3 class="mb-1">{{ number_format($expiringSoonServices) }}</h3>
                <p class="text-muted mb-0">Sắp hết hạn</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Dịch vụ sắp hết hạn -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2 text-warning"></i>
                    Dịch vụ sắp hết hạn (7 ngày tới)
                </h5>
                <a href="{{ route('admin.customer-services.index', ['filter' => 'expiring']) }}" 
                   class="btn btn-sm btn-outline-warning">
                    Xem tất cả
                </a>
            </div>
            <div class="card-body">
                @if($expiringSoon->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Khách hàng</th>
                                    <th>Dịch vụ</th>
                                    <th>Hết hạn</th>
                                    <th>Còn lại</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($expiringSoon as $service)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $service->customer->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $service->customer->customer_code }}</small>
                                            </div>
                                        </td>
                                        <td>{{ $service->servicePackage->name }}</td>
                                        <td>{{ $service->expires_at->format('d/m/Y') }}</td>
                                        <td>
                                            <span class="badge bg-warning">
                                                {{ $service->expires_at->diffInDays(now()) }} ngày
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.customers.show', $service->customer) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted mb-0">Không có dịch vụ nào sắp hết hạn</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Khách hàng mới nhất -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2 text-primary"></i>
                    Khách hàng mới nhất
                </h5>
            </div>
            <div class="card-body">
                @if($recentCustomers->count() > 0)
                    @foreach($recentCustomers as $customer)
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">{{ $customer->name }}</div>
                                <small class="text-muted">
                                    {{ $customer->customer_code }} • 
                                    {{ $customer->customerServices->count() }} dịch vụ
                                </small>
                            </div>
                            <a href="{{ route('admin.customers.show', $customer) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-users fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Chưa có khách hàng nào</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Dịch vụ phổ biến -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2 text-success"></i>
                    Dịch vụ phổ biến nhất
                </h5>
            </div>
            <div class="card-body">
                @if($popularServices->count() > 0)
                    <div class="row">
                        @foreach($popularServices as $service)
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="bg-light rounded p-3 mb-2">
                                        <i class="fas fa-box fa-2x text-primary mb-2"></i>
                                        <h6 class="mb-1">{{ $service->name }}</h6>
                                        <span class="badge bg-primary">
                                            {{ $service->customer_services_count }} khách hàng
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Chưa có dữ liệu thống kê</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Thao tác nhanh
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ route('admin.customers.create') }}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-user-plus me-2"></i>
                            Thêm khách hàng
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.service-packages.create') }}" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-box me-2"></i>
                            Thêm gói dịch vụ
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.customer-services.create') }}" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-link me-2"></i>
                            Gán dịch vụ
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('lookup.index') }}" class="btn btn-warning w-100 mb-2" target="_blank">
                            <i class="fas fa-search me-2"></i>
                            Trang tra cứu
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ServicePackage extends Model
{
    protected $fillable = [
        'category_id',
        'name',
        'account_type',
        'default_duration_days',
        'price',
        'description',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class, 'category_id');
    }

    public function customerServices(): HasMany
    {
        return $this->hasMany(CustomerService::class);
    }

    public function activeCustomerServices(): Has<PERSON>any
    {
        return $this->hasMany(CustomerService::class)->where('status', 'active');
    }

    // Scope để lấy các gói đang hoạt động
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}

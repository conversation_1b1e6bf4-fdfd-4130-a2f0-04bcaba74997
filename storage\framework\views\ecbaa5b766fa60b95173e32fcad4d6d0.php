<?php $__env->startSection('title', 'Quản lý gói dịch vụ'); ?>
<?php $__env->startSection('page-title', 'Quản lý gói dịch vụ'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-box me-2"></i>
            Danh sách gói dịch vụ
        </h5>
        <a href="<?php echo e(route('admin.service-packages.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Thêm gói dịch vụ
        </a>
    </div>
    
    <div class="card-body">
        <!-- Filters -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Tìm kiếm gói dịch vụ..."
                               value="<?php echo e(request('search')); ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="category_id" class="form-select">
                        <option value="">Tất cả danh mục</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" 
                                    <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>
                            Hoạt động
                        </option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>
                            Tạm dừng
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Lọc
                    </button>
                    <?php if(request()->hasAny(['search', 'category_id', 'status'])): ?>
                        <a href="<?php echo e(route('admin.service-packages.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </form>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">
                Hiển thị <?php echo e($servicePackages->firstItem() ?? 0); ?> - <?php echo e($servicePackages->lastItem() ?? 0); ?> 
                trong tổng số <?php echo e($servicePackages->total()); ?> gói dịch vụ
            </div>
        </div>
        
        <!-- Service Packages Table -->
        <?php if($servicePackages->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Tên gói</th>
                            <th>Danh mục</th>
                            <th>Loại tài khoản</th>
                            <th>Thời hạn</th>
                            <th>Giá</th>
                            <th>Khách hàng</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $servicePackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo e($package->name); ?></strong>
                                        <?php if($package->description): ?>
                                            <br>
                                            <small class="text-muted"><?php echo e(Str::limit($package->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e($package->category->name); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($package->account_type); ?></span>
                                </td>
                                <td>
                                    <?php echo e($package->default_duration_days); ?> ngày
                                </td>
                                <td>
                                    <strong><?php echo e(number_format($package->price)); ?>đ</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?php echo e($package->customerServices->count()); ?> khách hàng
                                    </span>
                                </td>
                                <td>
                                    <?php if($package->is_active): ?>
                                        <span class="badge bg-success">Hoạt động</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Tạm dừng</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.service-packages.show', $package)); ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.service-packages.edit', $package)); ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" 
                                              action="<?php echo e(route('admin.service-packages.destroy', $package)); ?>" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa gói dịch vụ này?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                <?php echo e($servicePackages->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy gói dịch vụ nào</h5>
                <?php if(request()->hasAny(['search', 'category_id', 'status'])): ?>
                    <p class="text-muted">Thử thay đổi bộ lọc hoặc <a href="<?php echo e(route('admin.service-packages.index')); ?>">xóa bộ lọc</a></p>
                <?php else: ?>
                    <p class="text-muted">Hãy <a href="<?php echo e(route('admin.service-packages.create')); ?>">thêm gói dịch vụ đầu tiên</a></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/service-packages/index.blade.php ENDPATH**/ ?>
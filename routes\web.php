<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\CustomerController;
use App\Http\Controllers\Admin\ServicePackageController;
use App\Http\Controllers\Admin\CustomerServiceController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\LookupController;

// Trang chủ - chuyển hướng đến admin dashboard
Route::get('/', function () {
    return redirect()->route('admin.dashboard');
});

// Trang tra cứu công khai
Route::get('/tra-cuu', [LookupController::class, 'index'])->name('lookup.index');

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Quản lý khách hàng
    Route::resource('customers', CustomerController::class);

    // Quản lý gói dịch vụ
    Route::resource('service-packages', ServicePackageController::class);

    // Quản lý dịch vụ khách hàng
    Route::resource('customer-services', CustomerServiceController::class);

    // Route để gán dịch vụ cho khách hàng
    Route::get('customers/{customer}/assign-service', [CustomerServiceController::class, 'assignForm'])
        ->name('customers.assign-service');
    Route::post('customers/{customer}/assign-service', [CustomerServiceController::class, 'assignService'])
        ->name('customers.store-service');
});

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\ServicePackage;
use App\Models\CustomerService;

class DashboardController extends Controller
{
    public function index()
    {
        // Thống kê tổng quan
        $totalCustomers = Customer::count();
        $totalServicePackages = ServicePackage::count();
        $totalActiveServices = CustomerService::where('status', 'active')->count();
        $expiringSoonServices = CustomerService::expiringSoon()->count();

        // Dịch vụ sắp hết hạn (7 ngày tới)
        $expiringSoon = CustomerService::with(['customer', 'servicePackage'])
            ->expiringSoon()
            ->orderBy('expires_at')
            ->limit(10)
            ->get();

        // Khách hàng mới nhất
        $recentCustomers = Customer::with('customerServices')
            ->latest()
            ->limit(5)
            ->get();

        // Dịch vụ phổ biến nhất
        $popularServices = ServicePackage::withCount('customerServices')
            ->orderBy('customer_services_count', 'desc')
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact(
            'totalCustomers',
            'totalServicePackages',
            'totalActiveServices',
            'expiringSoonServices',
            'expiringSoon',
            'recentCustomers',
            'popularServices'
        ));
    }
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tra c<PERSON>u dịch vụ - <PERSON>enUnlocked</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .lookup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .brand-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .brand-header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .brand-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .search-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .search-input {
            border-radius: 50px;
            border: 2px solid #e9ecef;
            padding: 15px 25px;
            font-size: 1.1rem;
            transition: all 0.3s;
        }
        
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .search-btn {
            border-radius: 50px;
            padding: 15px 30px;
            font-size: 1.1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            transition: transform 0.3s;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
        }
        
        .result-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .customer-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
        }
        
        .service-item {
            border-bottom: 1px solid #f8f9fa;
            padding: 20px;
            transition: background-color 0.3s;
        }
        
        .service-item:hover {
            background-color: #f8f9fa;
        }
        
        .service-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-expired {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-expiring {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .no-result {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 50px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="lookup-container">
        <!-- Brand Header -->
        <div class="brand-header">
            <h1>
                <i class="fas fa-crown me-3"></i>
                KienUnlocked
            </h1>
            <p>Tra cứu thông tin dịch vụ của bạn</p>
        </div>
        
        <!-- Search Form -->
        <div class="search-card">
            <form method="GET" action="<?php echo e(route('lookup.index')); ?>">
                <div class="row align-items-center">
                    <div class="col-md-8 mb-3 mb-md-0">
                        <input type="text" 
                               name="code" 
                               class="form-control search-input" 
                               placeholder="Nhập mã tra cứu của bạn (VD: KUN12345)"
                               value="<?php echo e($code); ?>"
                               required>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary search-btn w-100">
                            <i class="fas fa-search me-2"></i>
                            Tra cứu
                        </button>
                    </div>
                </div>
            </form>
            
            <?php if($code && !$customer): ?>
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Không tìm thấy thông tin với mã <strong><?php echo e($code); ?></strong>. 
                    Vui lòng kiểm tra lại mã tra cứu.
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Results -->
        <?php if($customer): ?>
            <div class="result-card">
                <!-- Customer Info -->
                <div class="customer-info">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-2">
                                <i class="fas fa-user me-2"></i>
                                <?php echo e($customer->name); ?>

                            </h3>
                            <p class="mb-1">
                                <i class="fas fa-id-card me-2"></i>
                                Mã khách hàng: <strong><?php echo e($customer->customer_code); ?></strong>
                            </p>
                            <?php if($customer->email): ?>
                                <p class="mb-1">
                                    <i class="fas fa-envelope me-2"></i>
                                    <?php echo e($customer->email); ?>

                                </p>
                            <?php endif; ?>
                            <?php if($customer->phone): ?>
                                <p class="mb-0">
                                    <i class="fas fa-phone me-2"></i>
                                    <?php echo e($customer->phone); ?>

                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="bg-white bg-opacity-20 rounded p-3">
                                <h4 class="mb-1"><?php echo e($services->count()); ?></h4>
                                <small>Dịch vụ đang sử dụng</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Services List -->
                <?php if($services->count() > 0): ?>
                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="service-item">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-1"><?php echo e($service->servicePackage->name); ?></h5>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-tag me-1"></i>
                                        <?php echo e($service->servicePackage->category->name); ?>

                                    </p>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-user-tag me-1"></i>
                                        <?php echo e($service->servicePackage->account_type); ?>

                                    </p>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <strong>Email đăng nhập:</strong>
                                        <br>
                                        <code class="bg-light p-1 rounded"><?php echo e($service->login_email); ?></code>
                                    </div>
                                </div>
                                <div class="col-md-3 text-md-end">
                                    <div class="mb-2">
                                        <strong>Hết hạn:</strong> <?php echo e($service->expires_at->format('d/m/Y')); ?>

                                    </div>
                                    <div>
                                        <?php if($service->status === 'active'): ?>
                                            <?php if($service->isExpired()): ?>
                                                <span class="status-badge status-expired">
                                                    <i class="fas fa-times-circle me-1"></i>
                                                    Đã hết hạn
                                                </span>
                                            <?php elseif($service->isExpiringSoon()): ?>
                                                <span class="status-badge status-expiring">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    Sắp hết hạn (<?php echo e($service->getDaysRemaining()); ?> ngày)
                                                </span>
                                            <?php else: ?>
                                                <span class="status-badge status-active">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    Hoạt động (<?php echo e($service->getDaysRemaining()); ?> ngày)
                                                </span>
                                            <?php endif; ?>
                                        <?php elseif($service->status === 'expired'): ?>
                                            <span class="status-badge status-expired">
                                                <i class="fas fa-times-circle me-1"></i>
                                                Đã hết hạn
                                            </span>
                                        <?php else: ?>
                                            <span class="status-badge status-expired">
                                                <i class="fas fa-ban me-1"></i>
                                                Đã hủy
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="no-result">
                        <i class="fas fa-box-open fa-3x mb-3"></i>
                        <h5>Chưa có dịch vụ nào</h5>
                        <p class="text-muted">Khách hàng này chưa được gán dịch vụ nào.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Footer -->
        <div class="footer">
            <p class="mb-1">
                <i class="fas fa-shield-alt me-2"></i>
                Thông tin được bảo mật và chỉ hiển thị cho chủ sở hữu
            </p>
            <p class="mb-0">
                © <?php echo e(date('Y')); ?> KienUnlocked. All rights reserved.
            </p>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/lookup/index.blade.php ENDPATH**/ ?>
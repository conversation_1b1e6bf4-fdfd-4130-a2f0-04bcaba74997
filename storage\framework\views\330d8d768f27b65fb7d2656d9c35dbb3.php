<?php $__env->startSection('title', 'Chi tiết khách hàng'); ?>
<?php $__env->startSection('page-title', 'Chi tiết khách hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Thông tin khách hàng -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Thông tin khách hàng
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Mã KH:</strong></td>
                        <td><span class="badge bg-primary"><?php echo e($customer->customer_code); ?></span></td>
                    </tr>
                    <tr>
                        <td><strong>Tên:</strong></td>
                        <td><?php echo e($customer->name); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>
                            <?php if($customer->email): ?>
                                <a href="mailto:<?php echo e($customer->email); ?>"><?php echo e($customer->email); ?></a>
                            <?php else: ?>
                                <span class="text-muted">Chưa có</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>SĐT:</strong></td>
                        <td>
                            <?php if($customer->phone): ?>
                                <a href="tel:<?php echo e($customer->phone); ?>"><?php echo e($customer->phone); ?></a>
                            <?php else: ?>
                                <span class="text-muted">Chưa có</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Ngày tạo:</strong></td>
                        <td><?php echo e($customer->created_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                </table>
                
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.customers.edit', $customer)); ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        Chỉnh sửa
                    </a>
                    <a href="<?php echo e(route('admin.customers.assign-service', $customer)); ?>" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        Gán dịch vụ mới
                    </a>
                    <a href="<?php echo e(route('lookup.index', ['code' => $customer->customer_code])); ?>" 
                       class="btn btn-info" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>
                        Xem trang tra cứu
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Danh sách dịch vụ -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Dịch vụ đang sử dụng (<?php echo e($customer->customerServices->count()); ?>)
                </h5>
                <a href="<?php echo e(route('admin.customers.assign-service', $customer)); ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Thêm dịch vụ
                </a>
            </div>
            <div class="card-body">
                <?php if($customer->customerServices->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Dịch vụ</th>
                                    <th>Loại TK</th>
                                    <th>Email đăng nhập</th>
                                    <th>Hết hạn</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $customer->customerServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo e($service->servicePackage->name); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo e($service->servicePackage->category->name); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo e($service->servicePackage->account_type); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <code><?php echo e($service->login_email); ?></code>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo e($service->expires_at->format('d/m/Y')); ?>

                                                <br>
                                                <small class="text-muted">
                                                    <?php if($service->isExpired()): ?>
                                                        <span class="text-danger">Đã hết hạn</span>
                                                    <?php elseif($service->isExpiringSoon()): ?>
                                                        <span class="text-warning">Sắp hết hạn</span>
                                                    <?php else: ?>
                                                        Còn <?php echo e($service->expires_at->diffInDays(now())); ?> ngày
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if($service->status === 'active'): ?>
                                                <span class="badge bg-success">Hoạt động</span>
                                            <?php elseif($service->status === 'expired'): ?>
                                                <span class="badge bg-danger">Hết hạn</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Đã hủy</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.customer-services.edit', $service)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="<?php echo e(route('admin.customer-services.destroy', $service)); ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Khách hàng chưa có dịch vụ nào</h6>
                        <a href="<?php echo e(route('admin.customers.assign-service', $customer)); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Gán dịch vụ đầu tiên
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="mt-3">
    <a href="<?php echo e(route('admin.customers.index')); ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Quay lại danh sách
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/customers/show.blade.php ENDPATH**/ ?>
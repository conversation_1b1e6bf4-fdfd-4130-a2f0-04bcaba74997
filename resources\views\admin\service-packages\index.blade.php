@extends('layouts.admin')

@section('title', 'Quản lý gói dịch vụ')
@section('page-title', 'Quản lý gói dịch vụ')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-box me-2"></i>
            Danh sách gói dịch vụ
        </h5>
        <a href="{{ route('admin.service-packages.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Thêm gói dịch vụ
        </a>
    </div>
    
    <div class="card-body">
        <!-- Filters -->
        <form method="GET" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Tìm kiếm gói dịch vụ..."
                               value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="category_id" class="form-select">
                        <option value="">Tất cả danh mục</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" 
                                    {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                            Hoạt động
                        </option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                            Tạm dừng
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Lọc
                    </button>
                    @if(request()->hasAny(['search', 'category_id', 'status']))
                        <a href="{{ route('admin.service-packages.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </a>
                    @endif
                </div>
            </div>
        </form>
        
        <!-- Results Info -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">
                Hiển thị {{ $servicePackages->firstItem() ?? 0 }} - {{ $servicePackages->lastItem() ?? 0 }} 
                trong tổng số {{ $servicePackages->total() }} gói dịch vụ
            </div>
        </div>
        
        <!-- Service Packages Table -->
        @if($servicePackages->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Tên gói</th>
                            <th>Danh mục</th>
                            <th>Loại tài khoản</th>
                            <th>Thời hạn</th>
                            <th>Giá</th>
                            <th>Khách hàng</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($servicePackages as $package)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $package->name }}</strong>
                                        @if($package->description)
                                            <br>
                                            <small class="text-muted">{{ Str::limit($package->description, 50) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $package->category->name }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ $package->account_type }}</span>
                                </td>
                                <td>
                                    {{ $package->default_duration_days }} ngày
                                </td>
                                <td>
                                    <strong>{{ number_format($package->price) }}đ</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{ $package->customerServices->count() }} khách hàng
                                    </span>
                                </td>
                                <td>
                                    @if($package->is_active)
                                        <span class="badge bg-success">Hoạt động</span>
                                    @else
                                        <span class="badge bg-danger">Tạm dừng</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.service-packages.show', $package) }}" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.service-packages.edit', $package) }}" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" 
                                              action="{{ route('admin.service-packages.destroy', $package) }}" 
                                              class="d-inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn xóa gói dịch vụ này?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $servicePackages->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy gói dịch vụ nào</h5>
                @if(request()->hasAny(['search', 'category_id', 'status']))
                    <p class="text-muted">Thử thay đổi bộ lọc hoặc <a href="{{ route('admin.service-packages.index') }}">xóa bộ lọc</a></p>
                @else
                    <p class="text-muted">Hãy <a href="{{ route('admin.service-packages.create') }}">thêm gói dịch vụ đầu tiên</a></p>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection
